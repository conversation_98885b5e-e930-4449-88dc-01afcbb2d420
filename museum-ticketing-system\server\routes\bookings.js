const express = require('express');
const {
  getBookings,
  getBooking,
  addBooking,
  updateBooking,
  deleteBooking,
  getMyBookings,
  processPayment
} = require('../controllers/bookings');

const router = express.Router({ mergeParams: true });

const { protect, authorize } = require('../middleware/auth');

// Public routes (none)

// Protected routes
router.use(protect);

// User routes
router.get('/mybookings', getMyBookings);
router.post('/:exhibitionId/bookings', addBooking);

// Admin routes
router.use(authorize('admin'));
router.get('/', getBookings);
router.get('/:id', getBooking);
router.put('/:id', updateBooking);
router.delete('/:id', deleteBooking);
router.post('/:id/payment', processPayment);

module.exports = router;
