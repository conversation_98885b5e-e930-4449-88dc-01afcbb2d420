const rateLimit = require('express-rate-limit');
const { RateLimiterMongo } = require('rate-limiter-flexible');
const mongoose = require('mongoose');

// Basic rate limiting middleware
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again after 15 minutes',
  standardHeaders: true,
  legacyHeaders: false,
});

// More advanced rate limiting using MongoDB (for distributed systems)
const mongoConn = mongoose.connection;

const mongoRateLimiter = new RateLimiterMongo({
  storeClient: mongoConn,
  points: 10, // 10 points
  duration: 1, // Per second
  blockDuration: 60 * 5, // Block for 5 minutes if exceeded
  keyPrefix: 'rlflx', // must be unique for limiters with different purpose
  dbName: 'museum_ticketing',
  tableName: 'rate_limiting'
});

const apiRateLimiter = async (req, res, next) => {
  try {
    const rateLimiterRes = await mongoRateLimiter.consume(req.ip);
    
    res.set({
      'Retry-After': rateLimiterRes.msBeforeNext / 1000,
      'X-RateLimit-Limit': mongoRateLimiter.points,
      'X-RateLimit-Remaining': rateLimiterRes.remainingPoints,
      'X-RateLimit-Reset': new Date(Date.now() + rateLimiterRes.msBeforeNext)
    });
    
    next();
  } catch (rateLimiterRes) {
    res.set({
      'Retry-After': rateLimiterRes.msBeforeNext / 1000,
      'X-RateLimit-Limit': mongoRateLimiter.points,
      'X-RateLimit-Remaining': rateLimiterRes.remainingPoints,
      'X-RateLimit-Reset': new Date(Date.now() + rateLimiterRes.msBeforeNext)
    });
    
    return res.status(429).json({
      success: false,
      message: 'Too many requests, please try again later.',
      retryAfter: Math.ceil(rateLimiterRes.msBeforeNext / 1000) + ' seconds'
    });
  }
};

// Export both limiters - use as needed
module.exports = {
  apiLimiter,
  mongoRateLimiter: apiRateLimiter
};
