{"name": "museum-ticketing-client", "version": "1.0.0", "private": true, "dependencies": {"@date-io/date-fns": "^2.16.0", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.11.16", "@mui/lab": "^5.0.0-alpha.127", "@mui/material": "^5.13.0", "@mui/x-date-pickers": "^6.3.1", "@reduxjs/toolkit": "^1.9.5", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "apexcharts": "^3.40.0", "axios": "^1.3.6", "chart.js": "^4.2.1", "date-fns": "^2.29.3", "formik": "^2.2.9", "i18next": "^22.4.10", "i18next-browser-languagedetector": "^7.0.1", "i18next-http-backend": "^2.2.0", "jwt-decode": "^3.1.2", "leaflet": "^1.9.3", "react": "^18.2.0", "react-apexcharts": "^1.4.0", "react-chartjs-2": "^5.2.0", "react-cookie": "^4.1.1", "react-dom": "^18.2.0", "react-helmet-async": "^1.3.0", "react-i18next": "^12.2.0", "react-leaflet": "^4.2.1", "react-quill": "^2.0.0", "react-redux": "^8.0.5", "react-router-dom": "^6.10.0", "react-scripts": "5.0.1", "react-toastify": "^9.1.2", "recharts": "^2.6.2", "socket.io-client": "^4.6.1", "web-vitals": "^2.1.4", "yup": "^1.1.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .js,.jsx", "format": "prettier --write \"src/**/*.{js,jsx}\"", "prepare": "husky install"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@types/leaflet": "^1.9.8", "@types/node": "^16.18.31", "@types/react": "^18.2.6", "@types/react-dom": "^18.2.4", "@types/react-router-dom": "^5.3.3", "autoprefixer": "^10.4.14", "eslint": "^8.38.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "husky": "^8.0.3", "lint-staged": "^13.2.0", "postcss": "^8.4.23", "prettier": "^2.8.7", "tailwindcss": "^3.3.2", "typescript": "^5.0.4"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}}