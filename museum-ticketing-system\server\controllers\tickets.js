const ErrorResponse = require('../utils/errorResponse');
const asyncHandler = require('../middleware/async');
const Ticket = require('../models/Ticket');

// @desc    Get all tickets
// @route   GET /api/v1/tickets
// @access  Public
exports.getTickets = asyncHandler(async (req, res, next) => {
  const tickets = await Ticket.find({ isActive: true });

  res.status(200).json({
    success: true,
    count: tickets.length,
    data: tickets
  });
});

// @desc    Get single ticket
// @route   GET /api/v1/tickets/:id
// @access  Public
exports.getTicket = asyncHandler(async (req, res, next) => {
  const ticket = await Ticket.findById(req.params.id);

  if (!ticket || !ticket.isActive) {
    return next(
      new ErrorResponse(`Ticket not found with id of ${req.params.id}`, 404)
    );
  }

  res.status(200).json({
    success: true,
    data: ticket
  });
});

// @desc    Create new ticket
// @route   POST /api/v1/tickets
// @access  Private/Admin
exports.createTicket = asyncHandler(async (req, res, next) => {
  const ticket = await Ticket.create(req.body);

  res.status(201).json({
    success: true,
    data: ticket
  });
});

// @desc    Update ticket
// @route   PUT /api/v1/tickets/:id
// @access  Private/Admin
exports.updateTicket = asyncHandler(async (req, res, next) => {
  let ticket = await Ticket.findById(req.params.id);

  if (!ticket) {
    return next(
      new ErrorResponse(`Ticket not found with id of ${req.params.id}`, 404)
    );
  }

  ticket = await Ticket.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
    runValidators: true
  });

  res.status(200).json({ success: true, data: ticket });
});

// @desc    Delete ticket
// @route   DELETE /api/v1/tickets/:id
// @access  Private/Admin
exports.deleteTicket = asyncHandler(async (req, res, next) => {
  const ticket = await Ticket.findById(req.params.id);

  if (!ticket) {
    return next(
      new ErrorResponse(`Ticket not found with id of ${req.params.id}`, 404)
    );
  }

  // Soft delete by setting isActive to false
  ticket.isActive = false;
  await ticket.save();

  res.status(200).json({ success: true, data: {} });
});

// @desc    Get tickets by type
// @route   GET /api/v1/tickets/type/:type
// @access  Public
exports.getTicketsByType = asyncHandler(async (req, res, next) => {
  const tickets = await Ticket.find({
    type: req.params.type,
    isActive: true
  });

  res.status(200).json({
    success: true,
    count: tickets.length,
    data: tickets
  });
});
