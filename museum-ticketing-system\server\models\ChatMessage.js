const mongoose = require('mongoose');

const ChatMessageSchema = new mongoose.Schema({
  sessionId: {
    type: String,
    required: [true, 'Session ID is required'],
    index: true
  },
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    index: true
  },
  message: {
    type: String,
    required: [true, 'Message content is required'],
    trim: true
  },
  sender: {
    type: String,
    enum: ['user', 'bot'],
    required: [true, 'Sender type is required']
  },
  intent: {
    type: String,
    trim: true
  },
  entities: {
    type: Map,
    of: mongoose.Schema.Types.Mixed
  },
  context: {
    type: Map,
    of: mongoose.Schema.Types.Mixed
  },
  language: {
    type: String,
    default: 'en',
    enum: ['en', 'es', 'fr', 'de', 'hi', 'zh', 'ja', 'ko', 'ru', 'ar']
  },
  timestamp: {
    type: Date,
    default: Date.now
  },
  isRead: {
    type: Boolean,
    default: false
  },
  metadata: {
    type: Map,
    of: mongoose.Schema.Types.Mixed
  }
});

// Index for faster querying of conversation history
ChatMessageSchema.index({ sessionId: 1, timestamp: 1 });
ChatMessageSchema.index({ user: 1, timestamp: 1 });

// Static method to get conversation history
ChatMessageSchema.statics.getConversationHistory = async function(sessionId, limit = 20) {
  return await this.find({ sessionId })
    .sort({ timestamp: -1 })
    .limit(parseInt(limit))
    .sort({ timestamp: 1 }); // Return in chronological order
};

// Static method to mark messages as read
ChatMessageSchema.statics.markAsRead = async function(sessionId, userId = null) {
  const query = { sessionId, isRead: false };
  if (userId) {
    query.user = userId;
  }
  
  return await this.updateMany(
    query,
    { $set: { isRead: true } }
  );
};

module.exports = mongoose.model('ChatMessage', ChatMessageSchema);
