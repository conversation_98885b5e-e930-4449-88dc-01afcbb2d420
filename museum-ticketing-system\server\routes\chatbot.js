const express = require('express');
const {
  sendMessage,
  getChatHistory,
  getChatSessions
} = require('../controllers/chatbot');

const router = express.Router();

const { protect } = require('../middleware/auth');

// Public routes
router.post('/message', sendMessage);

// Protected routes
router.use(protect);
router.get('/history', getChatHistory);
router.get('/sessions', getChatSessions);

module.exports = router;
