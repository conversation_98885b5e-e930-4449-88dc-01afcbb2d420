import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { getCookie } from 'react-cookie';

const baseQuery = fetchBaseQuery({
  baseUrl: process.env.REACT_APP_API_URL,
  prepareHeaders: (headers) => {
    const token = getCookie('token');
    if (token) {
      headers.set('authorization', `Bearer ${token}`);
    }
    return headers;
  },
});

export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: async (args, api, extraOptions) => {
    const result = await baseQuery(args, api, extraOptions);
    
    // Handle 401 Unauthorized responses
    if (result.error?.status === 401) {
      // Handle token refresh or redirect to login
      // You can dispatch an action to handle logout
    }
    
    return result;
  },
  tagTypes: [
    'User',
    'Ticket',
    'Exhibition',
    'Booking',
    'Chat',
  ],
  endpoints: (builder) => ({}),
});

export const {} = apiSlice;
