const fs = require('fs');
const path = require('path');
const PDFDocument = require('pdfkit');
const QRCode = require('qrcode');

/**
 * Generate a PDF ticket for a booking
 * @param {Object} booking - Booking details
 * @param {Object} exhibition - Exhibition details
 * @param {Object} ticket - Ticket details
 * @returns {Promise<Buffer>} - PDF buffer
 */
const generateTicketPDF = async (booking, exhibition, ticket) => {
  return new Promise(async (resolve, reject) => {
    try {
      // Create a new PDF document
      const doc = new PDFDocument({
        size: 'A6', // Smaller size for tickets
        margin: 20,
        layout: 'portrait'
      });

      // Collect the PDF data in a buffer
      const buffers = [];
      doc.on('data', buffers.push.bind(buffers));
      doc.on('end', () => {
        const pdfData = Buffer.concat(buffers);
        resolve(pdfData);
      });

      // Add background
      doc.rect(0, 0, doc.page.width, doc.page.height)
        .fill('#f8f9fa');

      // Add header
      doc.fill('#212529')
        .fontSize(16)
        .font('Helvetica-Bold')
        .text('Museum Ticket', { align: 'center' })
        .moveDown(0.5);

      // Add exhibition title
      doc.fill('#0d6efd')
        .fontSize(14)
        .font('Helvetica-Bold')
        .text(exhibition.title, { align: 'center', underline: true })
        .moveDown(0.5);

      // Add ticket details
      doc.fill('#212529')
        .fontSize(10)
        .font('Helvetica')
        .text(`Ticket Type: ${ticket.name}`, { align: 'left' })
        .text(`Price: $${ticket.price.toFixed(2)}`, { align: 'left' })
        .text(`Date: ${new Date(booking.visitDate).toLocaleDateString()}`, { align: 'left' })
        .text(`Time: ${new Date(booking.visitDate).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`, { align: 'left' })
        .text(`Visitors: ${booking.numberOfVisitors}`, { align: 'left' })
        .moveDown(1);

      // Add location
      doc.fill('#6c757d')
        .fontSize(9)
        .text(`Location: ${exhibition.location}`, { align: 'left' })
        .moveDown(1);

      // Generate QR code data URL
      const qrData = {
        bookingId: booking._id,
        exhibitionId: exhibition._id,
        ticketId: ticket._id,
        visitDate: booking.visitDate,
        numberOfVisitors: booking.numberOfVisitors
      };

      const qrCodeDataUrl = await QRCode.toDataURL(JSON.stringify(qrData));
      
      // Convert data URL to buffer
      const qrCodeBuffer = Buffer.from(qrCodeDataUrl.split(',')[1], 'base64');
      
      // Add QR code to PDF
      doc.image(qrCodeBuffer, {
        fit: [100, 100],
        align: 'center',
        valign: 'center'
      });

      // Add ticket ID
      doc.fontSize(8)
        .fill('#6c757d')
        .text(`Ticket ID: ${booking._id}`, { align: 'center' });

      // Add terms and conditions
      doc.moveDown(1)
        .fontSize(7)
        .fill('#6c757d')
        .text('Terms & Conditions: This ticket is non-refundable and non-transferable. Please arrive 15 minutes before your scheduled time.', {
          align: 'center',
          width: doc.page.width - 40,
          lineGap: 3
        });

      // Finalize the PDF
      doc.end();
    } catch (error) {
      console.error('Error generating PDF:', error);
      reject(error);
    }
  });
};

/**
 * Save PDF to a file
 * @param {Buffer} pdfBuffer - PDF buffer
 * @param {string} fileName - Name of the file
 * @returns {Promise<string>} - Path to the saved file
 */
const savePDFToFile = async (pdfBuffer, fileName) => {
  return new Promise((resolve, reject) => {
    try {
      const uploadsDir = path.join(__dirname, '../../public/tickets');
      
      // Create uploads directory if it doesn't exist
      if (!fs.existsSync(uploadsDir)) {
        fs.mkdirSync(uploadsDir, { recursive: true });
      }
      
      const filePath = path.join(uploadsDir, `${fileName}.pdf`);
      
      fs.writeFile(filePath, pdfBuffer, (err) => {
        if (err) {
          reject(err);
        } else {
          resolve(`/tickets/${fileName}.pdf`);
        }
      });
    } catch (error) {
      reject(error);
    }
  });
};

module.exports = {
  generateTicketPDF,
  savePDFToFile
};
