<!DOCTYPE html>
<html lang="en" dir="ltr">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="theme-color" content="#1976d2" />
    <meta
      name="description"
      content="Museum Ticketing System - Your Gateway to Cultural Exploration. Book tickets for exhibitions, explore collections, and plan your museum visit."
    />
    <meta name="keywords" content="museum, tickets, exhibitions, cultural, booking, events, art, history" />
    <meta name="author" content="Museum Ticketing System" />
    <meta name="robots" content="index, follow" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://museum-ticketing.example.com/" />
    <meta property="og:title" content="Museum Ticketing System" />
    <meta property="og:description" content="Book tickets for museum exhibitions and explore cultural collections." />
    <meta property="og:image" content="%PUBLIC_URL%/og-image.jpg" />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://museum-ticketing.example.com/" />
    <meta property="twitter:title" content="Museum Ticketing System" />
    <meta property="twitter:description" content="Book tickets for museum exhibitions and explore cultural collections." />
    <meta property="twitter:image" content="%PUBLIC_URL%/og-image.jpg" />

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" sizes="180x180" href="%PUBLIC_URL%/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="%PUBLIC_URL%/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="%PUBLIC_URL%/favicon-16x16.png" />
    <link rel="mask-icon" href="%PUBLIC_URL%/safari-pinned-tab.svg" color="#1976d2" />
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link 
      href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Vazirmatn:wght@300;400;500;700&display=swap" 
      rel="stylesheet"
    />
    
    <!-- iOS PWA Support -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Museum Tickets" />
    
    <!-- MS Application -->
    <meta name="msapplication-TileColor" content="#1976d2" />
    <meta name="msapplication-config" content="%PUBLIC_URL%/browserconfig.xml" />
    
    <!-- Theme Color for Chrome, Firefox OS and Opera -->
    <meta name="theme-color" content="#1976d2" />
    <meta name="msapplication-navbutton-color" content="#1976d2" />
    <meta name="apple-mobile-web-app-status-bar-style" content="#1976d2" />
    
    <title>Museum Ticketing System | Book Your Museum Experience</title>
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="%PUBLIC_URL%/static/media/logo.5d5d9eef.svg" as="image" />
    
    <!-- Structured Data / Schema.org -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Museum",
      "name": "Museum Ticketing System",
      "description": "Book tickets for museum exhibitions and explore cultural collections.",
      "url": "https://museum-ticketing.example.com",
      "logo": "%PUBLIC_URL%/logo512.png",
      "sameAs": [
        "https://facebook.com/museumticketing",
        "https://twitter.com/museumticketing",
        "https://instagram.com/museumticketing"
      ],
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "******-555-5555",
        "contactType": "customer service"
      },
      "openingHoursSpecification": {
        "@type": "OpeningHoursSpecification",
        "dayOfWeek": [
          "Tuesday",
          "Wednesday",
          "Thursday",
          "Friday",
          "Saturday",
          "Sunday"
        ],
        "opens": "09:00",
        "closes": "17:00"
      },
      "priceRange": "$$"
    }
    </script>
  </head>
  <body>
    <noscript>
      <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
        <h1>JavaScript is disabled in your browser.</h1>
        <p>Please enable JavaScript to experience the full functionality of our website.</p>
        <p>Need help? <a href="https://www.enable-javascript.com/" target="_blank" rel="noopener noreferrer">See how to enable JavaScript</a>.</p>
      </div>
    </noscript>
    <div id="root"></div>
    
    <!-- Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-XXXXXXXXXX"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-XXXXXXXXXX');
    </script>
    
    <!-- Facebook Pixel Code -->
    <script>
      !function(f,b,e,v,n,t,s)
      {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
      n.callMethod.apply(n,arguments):n.queue.push(arguments)};
      if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
      n.queue=[];t=b.createElement(e);t.async=!0;
      t.src=v;s=b.getElementsByTagName(e)[0];
      s.parentNode.insertBefore(t,s)}(window, document,'script',
      'https://connect.facebook.net/en_US/fbevents.js');
      fbq('init', 'your-pixel-id');
      fbq('track', 'PageView');
    </script>
    <noscript>
      <img height="1" width="1" style="display:none" 
           src="https://www.facebook.com/tr?id=your-pixel-id&ev=PageView&noscript=1"
      />
    </noscript>
    <!-- End Facebook Pixel Code -->
  </body>
</html>
