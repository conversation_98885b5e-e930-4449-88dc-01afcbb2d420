import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  theme: 'light',
  language: 'en',
  isMobileMenuOpen: false,
  isChatOpen: false,
  notifications: [],
  loading: false,
  error: null,
  drawerOpen: false,
  alert: {
    open: false,
    message: '',
    severity: 'info', // 'error', 'warning', 'info', 'success'
  },
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    toggleTheme: (state) => {
      state.theme = state.theme === 'light' ? 'dark' : 'light';
      localStorage.setItem('theme', state.theme);
    },
    setLanguage: (state, action) => {
      state.language = action.payload;
      localStorage.setItem('language', action.payload);
    },
    toggleMobileMenu: (state) => {
      state.isMobileMenuOpen = !state.isMobileMenuOpen;
    },
    toggleChat: (state) => {
      state.isChatOpen = !state.isChatOpen;
    },
    addNotification: (state, action) => {
      state.notifications.push({
        id: Date.now(),
        ...action.payload,
      });
    },
    removeNotification: (state, action) => {
      state.notifications = state.notifications.filter(
        (notification) => notification.id !== action.payload
      );
    },
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    toggleDrawer: (state) => {
      state.drawerOpen = !state.drawerOpen;
    },
    setAlert: (state, action) => {
      state.alert = {
        open: true,
        ...action.payload,
      };
    },
    closeAlert: (state) => {
      state.alert.open = false;
    },
  },
});

export const {
  toggleTheme,
  setLanguage,
  toggleMobileMenu,
  toggleChat,
  addNotification,
  removeNotification,
  setLoading,
  setError,
  clearError,
  toggleDrawer,
  setAlert,
  closeAlert,
} = uiSlice.actions;

export const selectTheme = (state) => state.ui.theme;
export const selectLanguage = (state) => state.ui.language;
export const selectIsMobileMenuOpen = (state) => state.ui.isMobileMenuOpen;
export const selectIsChatOpen = (state) => state.ui.isChatOpen;
export const selectNotifications = (state) => state.ui.notifications;
export const selectUILoading = (state) => state.ui.loading;
export const selectUIError = (state) => state.ui.error;
export const selectDrawerOpen = (state) => state.ui.drawerOpen;
export const selectAlert = (state) => state.ui.alert;

export default uiSlice.reducer;
