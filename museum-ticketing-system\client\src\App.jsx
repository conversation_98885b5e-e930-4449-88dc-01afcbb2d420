import React, { useEffect, Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, StyledEngineProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { Helmet, HelmetProvider } from 'react-helmet-async';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// Import theme and components
import theme from './theme';
import Layout from './components/layout/Layout';
import LoadingSpinner from './components/common/LoadingSpinner';
import { selectTheme, selectLanguage } from './store/slices/uiSlice';
import { selectIsAuthenticated } from './store/slices/authSlice';

// Lazy load pages
const HomePage = React.lazy(() => import('./pages/HomePage'));
const LoginPage = React.lazy(() => import('./pages/auth/LoginPage'));
const RegisterPage = React.lazy(() => import('./pages/auth/RegisterPage'));
const ForgotPasswordPage = React.lazy(() => import('./pages/auth/ForgotPasswordPage'));
const ResetPasswordPage = React.lazy(() => import('./pages/auth/ResetPasswordPage'));
const ExhibitionsPage = React.lazy(() => import('./pages/exhibitions/ExhibitionsPage'));
const ExhibitionDetailPage = React.lazy(() => import('./pages/exhibitions/ExhibitionDetailPage'));
const TicketsPage = React.lazy(() => import('./pages/tickets/TicketsPage'));
const BookingsPage = React.lazy(() => import('./pages/bookings/BookingsPage'));
const BookingDetailPage = React.lazy(() => import('./pages/bookings/BookingDetailPage'));
const ProfilePage = React.lazy(() => import('./pages/profile/ProfilePage'));
const NotFoundPage = React.lazy(() => import('./pages/error/NotFoundPage'));

// Admin pages
const AdminDashboardPage = React.lazy(() => import('./pages/admin/DashboardPage'));
const AdminExhibitionsPage = React.lazy(() => import('./pages/admin/exhibitions/ExhibitionsPage'));
const AdminTicketsPage = React.lazy(() => import('./pages/admin/tickets/TicketsPage'));
const AdminBookingsPage = React.lazy(() => import('./pages/admin/bookings/BookingsPage'));
const AdminUsersPage = React.lazy(() => import('./pages/admin/users/UsersPage'));

// Protected Route component
const ProtectedRoute = ({ children, requiredRole = null }) => {
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const user = useSelector((state) => state.auth.user);

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (requiredRole && user?.role !== requiredRole) {
    return <Navigate to="/" replace />;
  }

  return children;
};

function App() {
  const dispatch = useDispatch();
  const { i18n } = useTranslation();
  const currentTheme = useSelector(selectTheme);
  const currentLanguage = useSelector(selectLanguage);

  // Set document direction based on language
  useEffect(() => {
    document.documentElement.dir = currentLanguage === 'fa' ? 'rtl' : 'ltr';
    document.documentElement.lang = currentLanguage;
    i18n.changeLanguage(currentLanguage);
  }, [currentLanguage, i18n]);

  return (
    <HelmetProvider>
      <StyledEngineProvider injectFirst>
        <ThemeProvider theme={theme(currentTheme, currentLanguage)}>
          <CssBaseline />
          <Helmet>
            <title>{i18n.t('app.name')}</title>
            <meta name="description" content={i18n.t('app.slogan')} />
            <meta name="theme-color" content={theme(currentTheme, currentLanguage).palette.primary.main} />
          </Helmet>
          
          <Router>
            <Layout>
              <Suspense fallback={<LoadingSpinner fullScreen />}>
                <Routes>
                  {/* Public Routes */}
                  <Route path="/" element={<HomePage />} />
                  <Route path="/exhibitions" element={<ExhibitionsPage />} />
                  <Route path="/exhibitions/:id" element={<ExhibitionDetailPage />} />
                  <Route path="/tickets" element={<TicketsPage />} />
                  
                  {/* Auth Routes */}
                  <Route path="/login" element={<LoginPage />} />
                  <Route path="/register" element={<RegisterPage />} />
                  <Route path="/forgot-password" element={<ForgotPasswordPage />} />
                  <Route path="/reset-password/:token" element={<ResetPasswordPage />} />
                  
                  {/* Protected Routes */}
                  <Route
                    path="/bookings"
                    element={
                      <ProtectedRoute>
                        <BookingsPage />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/bookings/:id"
                    element={
                      <ProtectedRoute>
                        <BookingDetailPage />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/profile"
                    element={
                      <ProtectedRoute>
                        <ProfilePage />
                      </ProtectedRoute>
                    }
                  />
                  
                  {/* Admin Routes */}
                  <Route
                    path="/admin"
                    element={
                      <ProtectedRoute requiredRole="admin">
                        <AdminDashboardPage />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/admin/exhibitions"
                    element={
                      <ProtectedRoute requiredRole="admin">
                        <AdminExhibitionsPage />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/admin/tickets"
                    element={
                      <ProtectedRoute requiredRole="admin">
                        <AdminTicketsPage />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/admin/bookings"
                    element={
                      <ProtectedRoute requiredRole="admin">
                        <AdminBookingsPage />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/admin/users"
                    element={
                      <ProtectedRoute requiredRole="admin">
                        <AdminUsersPage />
                      </ProtectedRoute>
                    }
                  />
                  
                  {/* 404 Route */}
                  <Route path="*" element={<NotFoundPage />} />
                </Routes>
              </Suspense>
            </Layout>
          </Router>
          
          <ToastContainer
            position="top-right"
            autoClose={5000}
            hideProgressBar={false}
            newestOnTop={false}
            closeOnClick
            rtl={currentLanguage === 'fa'}
            pauseOnFocusLoss
            draggable
            pauseOnHover
            theme={currentTheme}
          />
        </ThemeProvider>
      </StyledEngineProvider>
    </HelmetProvider>
  );
}

export default App;
