const ErrorResponse = require('../utils/errorResponse');
const asyncHandler = require('../middleware/async');
const Exhibition = require('../models/Exhibition');

// @desc    Get all exhibitions
// @route   GET /api/v1/exhibitions
// @access  Public
exports.getExhibitions = asyncHandler(async (req, res, next) => {
  const { status, featured, limit, fields, sort, page, ...queryParams } = req.query;
  
  // Create query object
  const query = {};
  
  // Filter by status if provided
  if (status) {
    const now = new Date();
    if (status === 'upcoming') {
      query.startDate = { $gt: now };
    } else if (status === 'ongoing') {
      query.startDate = { $lte: now };
      query.endDate = { $gte: now };
    } else if (status === 'completed') {
      query.endDate = { $lt: now };
    }
  }
  
  // Filter by featured
  if (featured) {
    query.featured = featured === 'true';
  }
  
  // Add other query parameters
  Object.keys(queryParams).forEach(key => {
    query[key] = queryParams[key];
  });
  
  // Execute query
  let queryResult = Exhibition.find(query);
  
  // Select fields
  if (fields) {
    const fieldsList = fields.split(',').join(' ');
    queryResult = queryResult.select(fieldsList);
  }
  
  // Sort
  if (sort) {
    const sortBy = sort.split(',').join(' ');
    queryResult = queryResult.sort(sortBy);
  } else {
    queryResult = queryResult.sort('startDate');
  }
  
  // Pagination
  const pageNumber = parseInt(page, 10) || 1;
  const limitNumber = parseInt(limit, 10) || 10;
  const skip = (pageNumber - 1) * limitNumber;
  
  const total = await Exhibition.countDocuments(query);
  
  queryResult = queryResult.skip(skip).limit(limitNumber);
  
  // Execute final query
  const exhibitions = await queryResult;
  
  // Pagination result
  const pagination = {};
  
  if (skip + limitNumber < total) {
    pagination.next = {
      page: pageNumber + 1,
      limit: limitNumber
    };
  }
  
  if (skip > 0) {
    pagination.prev = {
      page: pageNumber - 1,
      limit: limitNumber
    };
  }
  
  res.status(200).json({
    success: true,
    count: exhibitions.length,
    pagination,
    data: exhibitions
  });
});

// @desc    Get single exhibition
// @route   GET /api/v1/exhibitions/:id
// @access  Public
exports.getExhibition = asyncHandler(async (req, res, next) => {
  const exhibition = await Exhibition.findById(req.params.id)
    .populate('availableTickets', 'name price type');

  if (!exhibition) {
    return next(
      new ErrorResponse(`Exhibition not found with id of ${req.params.id}`, 404)
    );
  }

  res.status(200).json({
    success: true,
    data: exhibition
  });
});

// @desc    Create new exhibition
// @route   POST /api/v1/exhibitions
// @access  Private/Admin
exports.createExhibition = asyncHandler(async (req, res, next) => {
  const exhibition = await Exhibition.create(req.body);

  res.status(201).json({
    success: true,
    data: exhibition
  });
});

// @desc    Update exhibition
// @route   PUT /api/v1/exhibitions/:id
// @access  Private/Admin
exports.updateExhibition = asyncHandler(async (req, res, next) => {
  let exhibition = await Exhibition.findById(req.params.id);

  if (!exhibition) {
    return next(
      new ErrorResponse(`Exhibition not found with id of ${req.params.id}`, 404)
    );
  }

  exhibition = await Exhibition.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
    runValidators: true
  });

  res.status(200).json({ success: true, data: exhibition });
});

// @desc    Delete exhibition
// @route   DELETE /api/v1/exhibitions/:id
// @access  Private/Admin
exports.deleteExhibition = asyncHandler(async (req, res, next) => {
  const exhibition = await Exhibition.findById(req.params.id);

  if (!exhibition) {
    return next(
      new ErrorResponse(`Exhibition not found with id of ${req.params.id}`, 404)
    );
  }

  await exhibition.remove();

  res.status(200).json({ success: true, data: {} });
});

// @desc    Get featured exhibitions
// @route   GET /api/v1/exhibitions/featured
// @access  Public
exports.getFeaturedExhibitions = asyncHandler(async (req, res, next) => {
  const exhibitions = await Exhibition.find({ featured: true, status: 'ongoing' })
    .sort('startDate')
    .limit(5);

  res.status(200).json({
    success: true,
    count: exhibitions.length,
    data: exhibitions
  });
});

// @desc    Get exhibitions by status
// @route   GET /api/v1/exhibitions/status/:status
// @access  Public
exports.getExhibitionsByStatus = asyncHandler(async (req, res, next) => {
  const { status } = req.params;
  const { limit = 10 } = req.query;
  
  const exhibitions = await Exhibition.getExhibitionsByStatus(status, limit);
  
  res.status(200).json({
    success: true,
    count: exhibitions.length,
    data: exhibitions
  });
});
