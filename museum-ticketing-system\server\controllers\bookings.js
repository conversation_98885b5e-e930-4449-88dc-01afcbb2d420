const ErrorResponse = require('../utils/errorResponse');
const asyncHandler = require('../middleware/async');
const Booking = require('../models/Booking');
const Exhibition = require('../models/Exhibition');
const Ticket = require('../models/Ticket');
const { v4: uuidv4 } = require('uuid');
const QRCode = require('qrcode');

// @desc    Get all bookings
// @route   GET /api/v1/bookings
// @route   GET /api/v1/exhibitions/:exhibitionId/bookings
// @access  Private/Admin
exports.getBookings = asyncHandler(async (req, res, next) => {
  if (req.params.exhibitionId) {
    const bookings = await Booking.find({ exhibition: req.params.exhibitionId })
      .populate({
        path: 'user',
        select: 'name email'
      })
      .populate({
        path: 'ticket',
        select: 'name price type'
      });

    return res.status(200).json({
      success: true,
      count: bookings.length,
      data: bookings
    });
  } else {
    res.status(200).json(res.advancedResults);
  }
});

// @desc    Get single booking
// @route   GET /api/v1/bookings/:id
// @access  Private
exports.getBooking = asyncHandler(async (req, res, next) => {
  const booking = await Booking.findById(req.params.id)
    .populate({
      path: 'user',
      select: 'name email'
    })
    .populate({
      path: 'ticket',
      select: 'name price type'
    })
    .populate({
      path: 'exhibition',
      select: 'title location startDate endDate'
    });

  if (!booking) {
    return next(
      new ErrorResponse(`No booking found with the id of ${req.params.id}`, 404)
    );
  }

  // Make sure user is booking owner or admin
  if (
    booking.user._id.toString() !== req.user.id &&
    req.user.role !== 'admin'
  ) {
    return next(
      new ErrorResponse(
        `User ${req.user.id} is not authorized to access this booking`,
        401
      )
    );
  }

  res.status(200).json({
    success: true,
    data: booking
  });
});

// @desc    Add booking
// @route   POST /api/v1/exhibitions/:exhibitionId/bookings
// @access  Private
exports.addBooking = asyncHandler(async (req, res, next) => {
  req.body.exhibition = req.params.exhibitionId;
  req.body.user = req.user.id;

  const exhibition = await Exhibition.findById(req.params.exhibitionId);

  if (!exhibition) {
    return next(
      new ErrorResponse(
        `No exhibition with the id of ${req.params.exhibitionId}`,
        404
      )
    );
  }

  // Check if ticket exists and is active
  const ticket = await Ticket.findById(req.body.ticket);
  if (!ticket || !ticket.isActive) {
    return next(
      new ErrorResponse(`No active ticket with the id of ${req.body.ticket}`, 404)
    );
  }

  // Check if booking date is valid
  const bookingDate = new Date(req.body.bookingDate);
  if (bookingDate < new Date() || bookingDate > exhibition.endDate) {
    return next(
      new ErrorResponse(
        `Booking date must be between today and ${exhibition.endDate}`,
        400
      )
    );
  }

  // Check if there's enough capacity
  if (exhibition.currentBookings + req.body.numberOfVisitors > exhibition.maxCapacity) {
    return next(
      new ErrorResponse('Not enough capacity for this booking', 400)
    );
  }

  // Calculate total amount
  const totalAmount = ticket.price * req.body.numberOfVisitors;
  req.body.totalAmount = totalAmount;

  // Generate QR code
  const qrData = {
    bookingId: uuidv4(),
    exhibitionId: exhibition._id,
    ticketId: ticket._id,
    userId: req.user.id,
    visitDate: req.body.visitDate,
    numberOfVisitors: req.body.numberOfVisitors,
    totalAmount: totalAmount
  };

  const qrCode = await QRCode.toDataURL(JSON.stringify(qrData));
  req.body.qrCode = qrCode;

  const booking = await Booking.create(req.body);

  // Update exhibition's current bookings count
  exhibition.currentBookings += req.body.numberOfVisitors;
  await exhibition.save();

  res.status(201).json({
    success: true,
    data: booking
  });
});

// @desc    Update booking
// @route   PUT /api/v1/bookings/:id
// @access  Private
exports.updateBooking = asyncHandler(async (req, res, next) => {
  let booking = await Booking.findById(req.params.id);

  if (!booking) {
    return next(
      new ErrorResponse(`No booking with the id of ${req.params.id}`, 404)
    );
  }

  // Make sure user is booking owner or admin
  if (booking.user.toString() !== req.user.id && req.user.role !== 'admin') {
    return next(
      new ErrorResponse(
        `User ${req.user.id} is not authorized to update this booking`,
        401
      )
    );
  }

  // Only allow updating specific fields
  const { status, paymentStatus, paymentId, specialRequests } = req.body;
  
  if (status) booking.status = status;
  if (paymentStatus) booking.paymentStatus = paymentStatus;
  if (paymentId) booking.paymentId = paymentId;
  if (specialRequests) booking.specialRequests = specialRequests;

  await booking.save();

  res.status(200).json({
    success: true,
    data: booking
  });
});

// @desc    Delete booking
// @route   DELETE /api/v1/bookings/:id
// @access  Private
exports.deleteBooking = asyncHandler(async (req, res, next) => {
  const booking = await Booking.findById(req.params.id);

  if (!booking) {
    return next(
      new ErrorResponse(`No booking with the id of ${req.params.id}`, 404)
    );
  }

  // Make sure user is booking owner or admin
  if (booking.user.toString() !== req.user.id && req.user.role !== 'admin') {
    return next(
      new ErrorResponse(
        `User ${req.user.id} is not authorized to delete this booking`,
        401
      )
    );
  }

  // Update exhibition's current bookings count
  const exhibition = await Exhibition.findById(booking.exhibition);
  if (exhibition) {
    exhibition.currentBookings = Math.max(0, exhibition.currentBookings - booking.numberOfVisitors);
    await exhibition.save();
  }

  await booking.remove();

  res.status(200).json({
    success: true,
    data: {}
  });
});

// @desc    Get bookings for current user
// @route   GET /api/v1/bookings/mybookings
// @access  Private
exports.getMyBookings = asyncHandler(async (req, res, next) => {
  const bookings = await Booking.find({ user: req.user.id })
    .populate({
      path: 'exhibition',
      select: 'title location startDate endDate image'
    })
    .populate({
      path: 'ticket',
      select: 'name price type'
    });

  res.status(200).json({
    success: true,
    count: bookings.length,
    data: bookings
  });
});

// @desc    Process payment for booking
// @route   POST /api/v1/bookings/:id/payment
// @access  Private
exports.processPayment = asyncHandler(async (req, res, next) => {
  const booking = await Booking.findById(req.params.id);

  if (!booking) {
    return next(
      new ErrorResponse(`No booking with the id of ${req.params.id}`, 404)
    );
  }

  // Make sure user is booking owner or admin
  if (booking.user.toString() !== req.user.id && req.user.role !== 'admin') {
    return next(
      new ErrorResponse(
        `User ${req.user.id} is not authorized to process payment for this booking`,
        401
      )
    );
  }

  // In a real application, you would integrate with a payment gateway here
  // For example, with Stripe, PayPal, etc.
  // This is a simplified example
  
  // Simulate payment processing
  const paymentSuccess = Math.random() > 0.1; // 90% success rate for demo
  
  if (paymentSuccess) {
    booking.paymentStatus = 'paid';
    booking.paymentId = `pay_${Math.random().toString(36).substr(2, 9)}`;
    booking.status = 'confirmed';
    await booking.save();
    
    // In a real app, you would send a confirmation email here
    
    return res.status(200).json({
      success: true,
      data: booking,
      message: 'Payment processed successfully'
    });
  } else {
    return next(new ErrorResponse('Payment failed. Please try again.', 400));
  }
});
