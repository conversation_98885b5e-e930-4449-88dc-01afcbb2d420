const express = require('express');
const {
  getTickets,
  getTicket,
  createTicket,
  updateTicket,
  deleteTicket,
  getTicketsByType
} = require('../controllers/tickets');

const router = express.Router({ mergeParams: true });

const { protect, authorize } = require('../middleware/auth');

router
  .route('/')
  .get(getTickets)
  .post(protect, authorize('admin'), createTicket);

router
  .route('/:id')
  .get(getTicket)
  .put(protect, authorize('admin'), updateTicket)
  .delete(protect, authorize('admin'), deleteTicket);

router.route('/type/:type').get(getTicketsByType);

module.exports = router;
