const mongoose = require('mongoose');

const BookingSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  ticket: {
    type: mongoose.Schema.ObjectId,
    ref: 'Ticket',
    required: true
  },
  exhibition: {
    type: mongoose.Schema.ObjectId,
    ref: 'Exhibition',
    required: true
  },
  bookingDate: {
    type: Date,
    required: [true, 'Please add a booking date']
  },
  visitDate: {
    type: Date,
    required: [true, 'Please add a visit date']
  },
  numberOfVisitors: {
    type: Number,
    required: [true, 'Please specify number of visitors'],
    min: [1, 'Number of visitors must be at least 1']
  },
  totalAmount: {
    type: Number,
    required: [true, 'Please add total amount']
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'paid', 'failed', 'refunded'],
    default: 'pending'
  },
  paymentId: {
    type: String
  },
  paymentMethod: {
    type: String,
    enum: ['credit_card', 'debit_card', 'net_banking', 'wallet', 'upi'],
    required: [true, 'Please specify payment method']
  },
  status: {
    type: String,
    enum: ['confirmed', 'cancelled', 'completed', 'no_show'],
    default: 'confirmed'
  },
  qrCode: {
    type: String
  },
  visitors: [{
    name: {
      type: String,
      required: [true, 'Please add visitor name']
    },
    age: {
      type: Number,
      min: [0, 'Age must be a positive number']
    },
    idType: {
      type: String,
      enum: ['aadhar', 'passport', 'driving_license', 'voter_id', 'other']
    },
    idNumber: String
  }],
  specialRequests: {
    type: String,
    maxlength: [500, 'Special requests cannot be more than 500 characters']
  },
  cancellationReason: String,
  cancelledAt: Date,
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
BookingSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Static method to get monthly revenue
BookingSchema.statics.getMonthlyRevenue = async function() {
  const currentDate = new Date();
  const lastYear = new Date(currentDate.setFullYear(currentDate.getFullYear() - 1));

  return await this.aggregate([
    {
      $match: {
        createdAt: { $gte: lastYear },
        paymentStatus: 'paid'
      }
    },
    {
      $project: {
        month: { $month: '$createdAt' },
        year: { $year: '$createdAt' },
        totalAmount: 1
      }
    },
    {
      $group: {
        _id: { month: '$month', year: '$year' },
        total: { $sum: '$totalAmount' },
        count: { $sum: 1 }
      }
    },
    {
      $sort: { '_id.year': 1, '_id.month': 1 }
    }
  ]);
};

module.exports = mongoose.model('Booking', BookingSchema);
