import { createSlice } from '@reduxjs/toolkit';
import { getCookie, setCookie, removeCookie } from 'react-cookie';
import { jwtDecode } from 'jwt-decode';

const token = getCookie('token');
let user = null;

if (token) {
  try {
    const decoded = jwtDecode(token);
    user = { id: decoded.id, email: decoded.email, role: decoded.role };
  } catch (error) {
    console.error('Invalid token:', error);
    removeCookie('token', { path: '/' });
  }
}

const initialState = {
  user: user,
  token: token || null,
  isAuthenticated: !!token,
  loading: false,
  error: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    loginStart: (state) => {
      state.loading = true;
      state.error = null;
    },
    loginSuccess: (state, action) => {
      const { token, user } = action.payload;
      state.user = user;
      state.token = token;
      state.isAuthenticated = true;
      state.loading = false;
      state.error = null;
      
      // Set cookie with expiration (default 30 days)
      const expires = new Date();
      expires.setDate(expires.getDate() + 30);
      setCookie('token', token, { path: '/', expires });
    },
    loginFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },
    logout: (state) => {
      state.user = null;
      state.token = null;
      state.isAuthenticated = false;
      state.loading = false;
      state.error = null;
      removeCookie('token', { path: '/' });
    },
    updateUser: (state, action) => {
      state.user = { ...state.user, ...action.payload };
    },
    clearError: (state) => {
      state.error = null;
    },
  },
});

export const {
  loginStart,
  loginSuccess,
  loginFailure,
  logout,
  updateUser,
  clearError,
} = authSlice.actions;

export const selectCurrentUser = (state) => state.auth.user;
export const selectIsAuthenticated = (state) => state.auth.isAuthenticated;
export const selectAuthLoading = (state) => state.auth.loading;
export const selectAuthError = (state) => state.auth.error;

export default authSlice.reducer;
