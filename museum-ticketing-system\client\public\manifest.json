{"short_name": "Museum Tickets", "name": "Museum Ticketing System", "description": "Book tickets for museum exhibitions and explore cultural collections", "icons": [{"src": "/android-chrome-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "any maskable"}, {"src": "/android-chrome-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "any maskable"}, {"src": "/apple-touch-icon.png", "sizes": "180x180", "type": "image/png", "purpose": "any maskable"}, {"src": "/favicon-32x32.png", "sizes": "32x32", "type": "image/png"}, {"src": "/favicon-16x16.png", "sizes": "16x16", "type": "image/png"}, {"src": "/safari-pinned-tab.svg", "sizes": "any", "type": "image/svg+xml", "purpose": "monochrome"}], "screenshots": [{"src": "/screenshots/homepage-mobile.png", "sizes": "360x800", "type": "image/png", "form_factor": "narrow", "label": "Mobile Homepage"}, {"src": "/screenshots/homepage-desktop.png", "sizes": "1920x1080", "type": "image/png", "form_factor": "wide", "label": "Desktop Homepage"}], "start_url": "/?source=pwa", "scope": "/", "display": "standalone", "display_override": ["window-controls-overlay", "standalone", "browser"], "orientation": "any", "theme_color": "#1976d2", "background_color": "#ffffff", "id": "/?source=pwa", "dir": "ltr", "lang": "en", "shortcuts": [{"name": "Current Exhibitions", "short_name": "Exhibitions", "description": "View current and upcoming exhibitions", "url": "/exhibitions?source=pwa", "icons": [{"src": "/icons/exhibition-192x192.png", "sizes": "192x192"}]}, {"name": "My Bookings", "short_name": "Bookings", "description": "View your ticket bookings", "url": "/bookings?source=pwa", "icons": [{"src": "/icons/booking-192x192.png", "sizes": "192x192"}]}, {"name": "Plan Your Visit", "short_name": "Visit", "description": "Plan your museum visit", "url": "/visit?source=pwa", "icons": [{"src": "/icons/visit-192x192.png", "sizes": "192x192"}]}, {"name": "Buy Tickets", "url": "/tickets?source=pwa", "description": "Purchase tickets for exhibitions"}, {"name": "Today's Events", "url": "/events/today?source=pwa", "description": "View today's events"}], "categories": ["education", "entertainment", "events", "tickets"], "splash_pages": null, "prefer_related_applications": false, "related_applications": [], "protocol_handlers": [{"protocol": "web+museum", "url": "/ticket/%s"}], "share_target": {"action": "/share", "method": "GET", "enctype": "application/x-www-form-urlencoded", "params": {"title": "title", "text": "text", "url": "url"}}, "file_handlers": [{"action": "/import-ticket", "accept": {"application/json": [".json"], "application/pdf": [".pdf"]}, "icons": [{"src": "/icons/import-192x192.png", "sizes": "192x192", "type": "image/png"}], "launch_type": "single-client"}], "edge_side_panel": {"preferred_width": 480}, "launch_handler": {"client_mode": "navigate-existing"}, "handle_links": "preferred", "permissions": ["clipboard-write", "clipboard-read", "geolocation"], "file_browser_handlers": [{"action": "/browse", "accept": {"application/pdf": [".pdf"]}}]}