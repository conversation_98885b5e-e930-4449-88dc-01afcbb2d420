const mongoose = require('mongoose');

const ExhibitionSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Please add a title'],
    trim: true,
    maxlength: [100, 'Title cannot be more than 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Please add a description']
  },
  shortDescription: {
    type: String,
    required: [true, 'Please add a short description'],
    maxlength: [200, 'Short description cannot be more than 200 characters']
  },
  startDate: {
    type: Date,
    required: [true, 'Please add a start date']
  },
  endDate: {
    type: Date,
    required: [true, 'Please add an end date']
  },
  location: {
    type: String,
    required: [true, 'Please add a location']
  },
  image: {
    type: String,
    default: 'no-photo.jpg'
  },
  gallery: [String],
  featured: {
    type: Boolean,
    default: false
  },
  status: {
    type: String,
    enum: ['upcoming', 'ongoing', 'completed', 'cancelled'],
    default: 'upcoming'
  },
  maxCapacity: {
    type: Number,
    required: [true, 'Please specify maximum capacity'],
    min: [1, 'Capacity must be at least 1']
  },
  currentBookings: {
    type: Number,
    default: 0
  },
  availableTickets: [{
    type: mongoose.Schema.ObjectId,
    ref: 'Ticket'
  }],
  tags: [{
    type: String,
    trim: true
  }],
  duration: {
    type: Number, // in minutes
    required: [true, 'Please specify duration in minutes']
  },
  highlights: [String],
  additionalInfo: {
    type: String,
    maxlength: [1000, 'Additional info cannot be more than 1000 characters']
  },
  termsAndConditions: [String],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the status based on current date
ExhibitionSchema.pre('save', function(next) {
  const now = new Date();
  this.updatedAt = now;
  
  if (this.startDate > now) {
    this.status = 'upcoming';
  } else if (this.endDate < now) {
    this.status = 'completed';
  } else if (this.startDate <= now && this.endDate >= now) {
    this.status = 'ongoing';
  }
  
  next();
});

// Static method to get exhibitions by status
ExhibitionSchema.statics.getExhibitionsByStatus = async function(status, limit = 10) {
  let query = {};
  const now = new Date();
  
  if (status === 'upcoming') {
    query.startDate = { $gt: now };
  } else if (status === 'ongoing') {
    query.startDate = { $lte: now };
    query.endDate = { $gte: now };
  } else if (status === 'completed') {
    query.endDate = { $lt: now };
  }
  
  return await this.find(query)
    .sort({ startDate: 1 })
    .limit(parseInt(limit));
};

module.exports = mongoose.model('Exhibition', ExhibitionSchema);
