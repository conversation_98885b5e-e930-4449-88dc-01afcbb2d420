const ErrorResponse = require('../utils/errorResponse');
const asyncHandler = require('../middleware/async');
const ChatMessage = require('../models/ChatMessage');
const { v4: uuidv4 } = require('uuid');

// @desc    Send message to chatbot
// @route   POST /api/v1/chatbot/message
// @access  Public
exports.sendMessage = asyncHandler(async (req, res, next) => {
  const { message, sessionId, language = 'en' } = req.body;
  
  // Create or use existing session
  const chatSessionId = sessionId || uuidv4();
  
  // Save user message
  const userMessage = await ChatMessage.create({
    sessionId: chatSessionId,
    user: req.user ? req.user.id : null,
    message,
    sender: 'user',
    language
  });
  
  // Process message and generate response (in a real app, this would call Dialogflow or another NLP service)
  const botResponse = await processMessage(message, language, chatSessionId, req.user);
  
  // Save bot response
  const botMessage = await ChatMessage.create({
    sessionId: chatSessionId,
    user: req.user ? req.user.id : null,
    message: botResponse.message,
    sender: 'bot',
    intent: botResponse.intent,
    entities: botResponse.entities,
    context: botResponse.context,
    language
  });
  
  res.status(200).json({
    success: true,
    data: {
      sessionId: chatSessionId,
      messages: [userMessage, botMessage]
    }
  });
});

// @desc    Get chat history
// @route   GET /api/v1/chatbot/history
// @access  Private
exports.getChatHistory = asyncHandler(async (req, res, next) => {
  const { sessionId, limit = 20 } = req.query;
  
  if (!sessionId) {
    return next(new ErrorResponse('Session ID is required', 400));
  }
  
  const messages = await ChatMessage.getConversationHistory(sessionId, limit);
  
  // Mark messages as read
  await ChatMessage.markAsRead(sessionId, req.user ? req.user.id : null);
  
  res.status(200).json({
    success: true,
    count: messages.length,
    data: messages
  });
});

// @desc    Get all user chat sessions
// @route   GET /api/v1/chatbot/sessions
// @access  Private
exports.getChatSessions = asyncHandler(async (req, res, next) => {
  if (!req.user) {
    return next(new ErrorResponse('Authentication required', 401));
  }
  
  // Get distinct session IDs for the user
  const sessions = await ChatMessage.aggregate([
    { $match: { user: req.user._id } },
    { $sort: { timestamp: -1 } },
    {
      $group: {
        _id: '$sessionId',
        lastMessage: { $first: '$$ROOT' },
        messageCount: { $sum: 1 },
        unreadCount: {
          $sum: {
            $cond: [{ $eq: ['$isRead', false] }, 1, 0]
          }
        }
      }
    },
    { $sort: { 'lastMessage.timestamp': -1 } }
  ]);
  
  res.status(200).json({
    success: true,
    count: sessions.length,
    data: sessions
  });
});

// Helper function to process messages and generate responses
async function processMessage(message, language, sessionId, user) {
  // Convert message to lowercase for easier processing
  const lowerMessage = message.toLowerCase();
  
  // Simple keyword matching for demo purposes
  // In a real app, you would use Dialogflow, RASA, or another NLP service
  if (lowerMessage.includes('hello') || lowerMessage.includes('hi') || lowerMessage.includes('hey')) {
    return {
      message: 'Hello! Welcome to the Museum Ticketing System. How can I assist you today?',
      intent: 'greeting',
      entities: {},
      context: {}
    };
  } else if (lowerMessage.includes('ticket') || lowerMessage.includes('book')) {
    return {
      message: 'I can help you book tickets. What type of ticket would you like? We have general admission, student, and family tickets available.',
      intent: 'ticket_booking',
      entities: {},
      context: { bookingIntent: true }
    };
  } else if (lowerMessage.includes('price') || lowerMessage.includes('cost') || lowerMessage.includes('how much')) {
    return {
      message: 'Our ticket prices are as follows:\n- General Admission: $15\n- Student (with ID): $10\n- Senior (65+): $12\n- Family Pass (2 adults + 2 children): $40\n\nWould you like to book tickets?',
      intent: 'price_inquiry',
      entities: {},
      context: { priceInfo: true }
    };
  } else if (lowerMessage.includes('exhibition') || lowerMessage.includes('exhibit') || lowerMessage.includes('show')) {
    return {
      message: 'We currently have these exhibitions:\n1. Ancient Civilizations (Ongoing)\n2. Modern Art Masters (Starting next week)\n3. Space Exploration (Coming soon)\n\nWould you like more information about any of these?',
      intent: 'exhibition_info',
      entities: {},
      context: { exhibitionInfo: true }
    };
  } else if (lowerMessage.includes('time') || lowerMessage.includes('hour') || lowerMessage.includes('open')) {
    return {
      message: 'Our opening hours are:\n- Monday to Friday: 9:00 AM - 6:00 PM\n- Saturday & Sunday: 10:00 AM - 8:00 PM\n\nLast entry is 1 hour before closing time.',
      intent: 'opening_hours',
      entities: {},
      context: { hoursInfo: true }
    };
  } else if (lowerMessage.includes('thank') || lowerMessage.includes('thanks')) {
    return {
      message: 'You\'re welcome! Is there anything else I can help you with?',
      intent: 'gratitude',
      entities: {},
      context: {}
    };
  } else if (lowerMessage.includes('help')) {
    return {
      message: 'I can help you with:\n- Booking tickets\n- Checking ticket prices\n- Information about current exhibitions\n- Opening hours\n- Directions to the museum\n\nWhat would you like to know?',
      intent: 'help',
      entities: {},
      context: { helpMenu: true }
    };
  } else {
    // Default response if no intent is matched
    return {
      message: 'I\'m not sure I understand. Could you rephrase that? Or type "help" to see what I can assist you with.',
      intent: 'default',
      entities: {},
      context: {}
    };
  }
}
