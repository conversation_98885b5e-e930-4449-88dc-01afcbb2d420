import React, { useState, useEffect } from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import { useTheme, styled } from '@mui/material/styles';
import { Box, CssBaseline, useMediaQuery } from '@mui/material';
import { useSelector, useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';

// Import components
import Header from './Header';
import Sidebar from './Sidebar';
import Footer from './Footer';
import LoadingSpinner from '../common/LoadingSpinner';
import ChatWidget from '../chat/ChatWidget';

// Import actions and selectors
import { toggleDrawer, closeDrawer } from '../../store/slices/uiSlice';
import { selectDrawerOpen, selectTheme } from '../../store/slices/uiSlice';

// Styled components
const Main = styled('main', { shouldForwardProp: (prop) => prop !== 'open' })(
  ({ theme, open }) => ({
    flexGrow: 1,
    padding: theme.spacing(3),
    transition: theme.transitions.create('margin', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.leavingScreen,
    }),
    marginLeft: 0,
    ...(open && {
      transition: theme.transitions.create('margin', {
        easing: theme.transitions.easing.easeOut,
        duration: theme.transitions.duration.enteringScreen,
      }),
      marginLeft: theme.custom.drawerWidth,
    }),
    [theme.breakpoints.down('md')]: {
      marginLeft: 0,
      padding: theme.spacing(2),
    },
  })
);

const Layout = () => {
  const theme = useTheme();
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const location = useLocation();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const drawerOpen = useSelector(selectDrawerOpen);
  const currentTheme = useSelector(selectTheme);
  const [loading, setLoading] = useState(false);
  const [showChat, setShowChat] = useState(false);

  // Close drawer on mobile when location changes
  useEffect(() => {
    if (isMobile) {
      dispatch(closeDrawer());
    }
  }, [location, isMobile, dispatch]);

  // Handle drawer toggle
  const handleDrawerToggle = () => {
    dispatch(toggleDrawer());
  };

  // Toggle chat widget
  const toggleChat = () => {
    setShowChat(!showChat);
  };

  // Simulate loading state (replace with actual loading logic)
  useEffect(() => {
    setLoading(true);
    const timer = setTimeout(() => {
      setLoading(false);
    }, 500);
    return () => clearTimeout(timer);
  }, [location]);

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      <CssBaseline />
      
      {/* Header */}
      <Header 
        onDrawerToggle={handleDrawerToggle} 
        onToggleChat={toggleChat}
      />
      
      {/* Sidebar */}
      <Sidebar 
        open={drawerOpen} 
        onClose={handleDrawerToggle} 
        variant={isMobile ? 'temporary' : 'persistent'}
      />
      
      {/* Main Content */}
      <Main 
        open={drawerOpen && !isMobile}
        sx={{
          backgroundColor: currentTheme === 'dark' 
            ? theme.palette.background.default 
            : theme.palette.grey[100],
          paddingTop: theme.custom.appBarHeight + theme.spacing(3),
          paddingBottom: theme.custom.footerHeight + theme.spacing(3),
          [theme.breakpoints.down('md')]: {
            paddingTop: theme.custom.appBarHeight + theme.spacing(2),
            paddingBottom: theme.custom.footerHeight + theme.spacing(2),
          },
        }}
      >
        {loading ? (
          <LoadingSpinner />
        ) : (
          <React.Suspense fallback={<LoadingSpinner />}>
            <Outlet />
          </React.Suspense>
        )}
      </Main>
      
      {/* Footer */}
      <Footer />
      
      {/* Chat Widget */}
      {showChat && (
        <ChatWidget onClose={toggleChat} />
      )}
    </Box>
  );
};

export default Layout;
