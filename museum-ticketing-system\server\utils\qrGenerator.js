const QRCode = require('qrcode');
const fs = require('fs');
const path = require('path');

// Directory to store QR code images
const qrCodeDir = path.join(__dirname, '../../public/qr-codes');

// Create the directory if it doesn't exist
if (!fs.existsSync(qrCodeDir)) {
  fs.mkdirSync(qrCodeDir, { recursive: true });
}

/**
 * Generate a QR code for a booking
 * @param {Object} data - Data to encode in the QR code
 * @param {string} [format='png'] - Format of the QR code (png, svg, utf8)
 * @returns {Promise<Object>} - Object containing the QR code data URL and file path
 */
const generateQRCode = async (data, format = 'png') => {
  try {
    const { bookingId } = data;
    const fileName = `booking_${bookingId}_${Date.now()}.${format}`;
    const filePath = path.join(qrCodeDir, fileName);
    
    // Generate QR code as data URL
    const qrDataUrl = await QRCode.toDataURL(JSON.stringify(data), {
      errorCorrectionLevel: 'H', // High error correction
      type: 'image/png', // Default to png for data URL
      margin: 1,
      scale: 8,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    });
    
    // Save QR code to file if format is png
    if (format === 'png') {
      await QRCode.toFile(filePath, JSON.stringify(data), {
        errorCorrectionLevel: 'H',
        type: 'png',
        margin: 1,
        scale: 8,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });
    }
    
    return {
      dataUrl: qrDataUrl,
      filePath: format === 'png' ? `/qr-codes/${fileName}` : null
    };
  } catch (error) {
    console.error('Error generating QR code:', error);
    throw new Error('Failed to generate QR code');
  }
};

/**
 * Delete a QR code file
 * @param {string} filePath - Path to the QR code file
 */
const deleteQRCode = (filePath) => {
  if (!filePath) return;
  
  const fullPath = path.join(__dirname, '../../public', filePath);
  
  if (fs.existsSync(fullPath)) {
    fs.unlink(fullPath, (err) => {
      if (err) console.error(`Error deleting QR code file: ${err}`);
    });
  }
};

module.exports = {
  generateQRCode,
  deleteQRCode
};
