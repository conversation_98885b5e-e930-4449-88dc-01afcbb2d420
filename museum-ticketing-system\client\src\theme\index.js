import { createTheme } from '@mui/material/styles';
import { faIR, enUS } from '@mui/material/locale';

// Extend the Theme interface to include custom properties
declare module '@mui/material/styles' {
  interface Theme {
    custom: {
      drawerWidth: number;
      appBarHeight: number;
      footerHeight: number;
    };
  }
  // Allow configuration using `createTheme`
  interface ThemeOptions {
    custom?: {
      drawerWidth?: number;
      appBarHeight?: number;
      footerHeight?: number;
    };
  }
}

// Create a theme instance with custom properties
const createAppTheme = (mode, language) => {
  const isRTL = language === 'fa';
  
  return createTheme(
    {
      direction: isRTL ? 'rtl' : 'ltr',
      palette: {
        mode,
        primary: {
          main: '#1976d2',
          light: '#42a5f5',
          dark: '#1565c0',
          contrastText: '#fff',
        },
        secondary: {
          main: '#9c27b0',
          light: '#ba68c8',
          dark: '#7b1fa2',
          contrastText: '#fff',
        },
        error: {
          main: '#d32f2f',
          light: '#ef5350',
          dark: '#c62828',
        },
        warning: {
          main: '#ed6c02',
          light: '#ff9800',
          dark: '#e65100',
        },
        info: {
          main: '#0288d1',
          light: '#03a9f4',
          dark: '#01579b',
        },
        success: {
          main: '#2e7d32',
          light: '#4caf50',
          dark: '#1b5e20',
        },
        background: {
          default: mode === 'light' ? '#f5f5f5' : '#121212',
          paper: mode === 'light' ? '#ffffff' : '#1e1e1e',
        },
        text: {
          primary: mode === 'light' ? 'rgba(0, 0, 0, 0.87)' : '#ffffff',
          secondary: mode === 'light' ? 'rgba(0, 0, 0, 0.6)' : 'rgba(255, 255, 255, 0.7)',
          disabled: mode === 'light' ? 'rgba(0, 0, 0, 0.38)' : 'rgba(255, 255, 255, 0.5)',
        },
      },
      typography: {
        fontFamily: [
          '-apple-system',
          'BlinkMacSystemFont',
          '"Segoe UI"',
          'Roboto',
          '"Helvetica Neue"',
          'Arial',
          'sans-serif',
          '"Apple Color Emoji"',
          '"Segoe UI Emoji"',
          '"Segoe UI Symbol"',
        ].join(','),
        h1: {
          fontSize: '2.5rem',
          fontWeight: 500,
          lineHeight: 1.2,
        },
        h2: {
          fontSize: '2rem',
          fontWeight: 500,
          lineHeight: 1.2,
        },
        h3: {
          fontSize: '1.75rem',
          fontWeight: 500,
          lineHeight: 1.2,
        },
        h4: {
          fontSize: '1.5rem',
          fontWeight: 500,
          lineHeight: 1.2,
        },
        h5: {
          fontSize: '1.25rem',
          fontWeight: 500,
          lineHeight: 1.2,
        },
        h6: {
          fontSize: '1rem',
          fontWeight: 500,
          lineHeight: 1.2,
        },
        button: {
          textTransform: 'none',
        },
      },
      shape: {
        borderRadius: 8,
      },
      components: {
        MuiButton: {
          styleOverrides: {
            root: {
              borderRadius: 8,
              textTransform: 'none',
              fontWeight: 500,
            },
            contained: {
              boxShadow: 'none',
              '&:hover': {
                boxShadow: 'none',
              },
            },
          },
        },
        MuiCard: {
          styleOverrides: {
            root: {
              borderRadius: 12,
              boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
              '&:hover': {
                boxShadow: '0 8px 30px 0 rgba(0,0,0,0.1)',
              },
            },
          },
        },
        MuiAppBar: {
          styleOverrides: {
            root: {
              boxShadow: '0 2px 10px 0 rgba(0,0,0,0.05)',
            },
          },
        },
        MuiDrawer: {
          styleOverrides: {
            paper: {
              borderRight: 'none',
              boxShadow: '2px 0 10px 0 rgba(0,0,0,0.05)',
            },
          },
        },
      },
      custom: {
        drawerWidth: 280,
        appBarHeight: 64,
        footerHeight: 60,
      },
    },
    language === 'fa' ? faIR : enUS
  );
};

export default createAppTheme;
