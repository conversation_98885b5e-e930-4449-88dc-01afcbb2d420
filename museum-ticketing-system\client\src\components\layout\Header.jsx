import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '@mui/material/styles';
import {
  AppBar,
  Toolbar,
  IconButton,
  Typography,
  Badge,
  Box,
  Avatar,
  Menu,
  MenuItem,
  Divider,
  ListItemIcon,
  Tooltip,
  useMediaQuery,
  Button,
  InputBase,
  alpha,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Search as SearchIcon,
  Notifications as NotificationsIcon,
  Chat as ChatIcon,
  Person as PersonIcon,
  Settings as SettingsIcon,
  Logout as LogoutIcon,
  Brightness4 as DarkIcon,
  Brightness7 as LightIcon,
  Translate as TranslateIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

// Import actions and selectors
import { toggleTheme, toggleChat, addNotification } from '../../store/slices/uiSlice';
import { logout } from '../../store/slices/authSlice';
import { selectTheme, selectNotifications } from '../../store/slices/uiSlice';
import { selectCurrentUser } from '../../store/slices/authSlice';

const Header = ({ onDrawerToggle, onToggleChat }) => {
  const theme = useTheme();
  const { t, i18n } = useTranslation();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const currentTheme = useSelector(selectTheme);
  const notifications = useSelector(selectNotifications);
  const user = useSelector(selectCurrentUser);
  
  // State for dropdown menus
  const [anchorEl, setAnchorEl] = useState(null);
  const [languageAnchorEl, setLanguageAnchorEl] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  
  const open = Boolean(anchorEl);
  const languageMenuOpen = Boolean(languageAnchorEl);
  
  // Handle profile menu open
  const handleProfileMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };
  
  // Handle profile menu close
  const handleMenuClose = () => {
    setAnchorEl(null);
  };
  
  // Handle language menu open
  const handleLanguageMenuOpen = (event) => {
    setLanguageAnchorEl(event.currentTarget);
  };
  
  // Handle language menu close
  const handleLanguageMenuClose = () => {
    setLanguageAnchorEl(null);
  };
  
  // Handle language change
  const handleLanguageChange = (language) => {
    i18n.changeLanguage(language);
    document.documentElement.lang = language;
    document.documentElement.dir = language === 'fa' ? 'rtl' : 'ltr';
    handleLanguageMenuClose();
  };
  
  // Handle theme toggle
  const handleThemeToggle = () => {
    dispatch(toggleTheme());
  };
  
  // Handle search submit
  const handleSearchSubmit = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery)}`);
      setSearchQuery('');
    }
  };
  
  // Handle logout
  const handleLogout = () => {
    dispatch(logout());
    navigate('/login');
    handleMenuClose();
  };
  
  // Handle profile click
  const handleProfileClick = () => {
    navigate('/profile');
    handleMenuClose();
  };
  
  // Handle settings click
  const handleSettingsClick = () => {
    navigate('/settings');
    handleMenuClose();
  };

  return (
    <AppBar
      position="fixed"
      sx={{
        width: { md: `calc(100% - ${theme.custom.drawerWidth}px)` },
        ml: { md: `${theme.custom.drawerWidth}px` },
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
        backgroundColor: theme.palette.background.paper,
        color: theme.palette.text.primary,
      }}
    >
      <Toolbar>
        {/* Menu Button (Mobile) */}
        <IconButton
          color="inherit"
          aria-label="open drawer"
          edge="start"
          onClick={onDrawerToggle}
          sx={{ mr: 2, display: { md: 'none' } }}
        >
          <MenuIcon />
        </IconButton>
        
        {/* Logo / App Name */}
        <Typography
          variant="h6"
          noWrap
          component="div"
          sx={{ 
            display: { xs: 'none', sm: 'block' },
            fontWeight: 700,
            color: theme.palette.primary.main,
          }}
        >
          {t('app.name')}
        </Typography>
        
        {/* Search Bar */}
        <Box 
          component="form" 
          onSubmit={handleSearchSubmit}
          sx={{
            position: 'relative',
            borderRadius: theme.shape.borderRadius,
            backgroundColor: alpha(theme.palette.common.white, 0.15),
            '&:hover': {
              backgroundColor: alpha(theme.palette.common.white, 0.25),
            },
            marginLeft: { xs: 0, sm: 3 },
            marginRight: { xs: 1, sm: 2 },
            width: '100%',
            maxWidth: 500,
            display: 'flex',
          }}
        >
          <InputBase
            placeholder={t('common.search')}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            sx={{
              color: 'inherit',
              width: '100%',
              padding: theme.spacing(1, 1, 1, 2),
            }}
            inputProps={{ 'aria-label': 'search' }}
          />
          <IconButton 
            type="submit" 
            sx={{ p: '10px' }}
            aria-label="search"
          >
            <SearchIcon />
          </IconButton>
        </Box>
        
        <Box sx={{ flexGrow: 1 }} />
        
        {/* Theme Toggle */}
        <Tooltip title={currentTheme === 'dark' ? t('common.lightMode') : t('common.darkMode')}>
          <IconButton 
            onClick={handleThemeToggle} 
            color="inherit"
            sx={{ ml: 1 }}
          >
            {currentTheme === 'dark' ? <LightIcon /> : <DarkIcon />}
          </IconButton>
        </Tooltip>
        
        {/* Language Selector */}
        <Tooltip title={t('common.language')}>
          <IconButton 
            onClick={handleLanguageMenuOpen} 
            color="inherit"
            sx={{ ml: 1 }}
          >
            <TranslateIcon />
          </IconButton>
        </Tooltip>
        
        {/* Language Menu */}
        <Menu
          anchorEl={languageAnchorEl}
          open={languageMenuOpen}
          onClose={handleLanguageMenuClose}
          onClick={handleLanguageMenuClose}
          PaperProps={{
            elevation: 0,
            sx: {
              overflow: 'visible',
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
              mt: 1.5,
              '& .MuiAvatar-root': {
                width: 32,
                height: 32,
                ml: -0.5,
                mr: 1,
              },
            },
          }}
          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        >
          <MenuItem onClick={() => handleLanguageChange('en')}>
            <Box component="span" sx={{ ml: 1 }}>English</Box>
          </MenuItem>
          <MenuItem onClick={() => handleLanguageChange('fa')}>
            <Box component="span" sx={{ ml: 1, fontFamily: 'Vazir, sans-serif' }}>فارسی</Box>
          </MenuItem>
        </Menu>
        
        {user ? (
          <>
            {/* Chat Button */}
            <Tooltip title={t('common.chat')}>
              <IconButton 
                color="inherit"
                onClick={onToggleChat}
                sx={{ ml: 1 }}
              >
                <Badge badgeContent={0} color="error">
                  <ChatIcon />
                </Badge>
              </IconButton>
            </Tooltip>
            
            {/* Notifications */}
            <Tooltip title={t('common.notifications')}>
              <IconButton color="inherit" sx={{ ml: 1 }}>
                <Badge badgeContent={notifications.length} color="error">
                  <NotificationsIcon />
                </Badge>
              </IconButton>
            </Tooltip>
            
            {/* User Profile */}
            <Box sx={{ display: 'flex', alignItems: 'center', ml: 2 }}>
              <Tooltip title={t('common.accountSettings')}>
                <IconButton
                  onClick={handleProfileMenuOpen}
                  size="small"
                  sx={{ ml: 2 }}
                  aria-controls={open ? 'account-menu' : undefined}
                  aria-haspopup="true"
                  aria-expanded={open ? 'true' : undefined}
                >
                  <Avatar 
                    alt={user.name || user.email} 
                    src={user.avatar} 
                    sx={{ width: 36, height: 36 }}
                  >
                    {(user.name || user.email || '').charAt(0).toUpperCase()}
                  </Avatar>
                </IconButton>
              </Tooltip>
            </Box>
            
            {/* Profile Menu */}
            <Menu
              anchorEl={anchorEl}
              id="account-menu"
              open={open}
              onClose={handleMenuClose}
              onClick={handleMenuClose}
              PaperProps={{
                elevation: 0,
                sx: {
                  overflow: 'visible',
                  filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
                  mt: 1.5,
                  '& .MuiAvatar-root': {
                    width: 32,
                    height: 32,
                    ml: -0.5,
                    mr: 1,
                  },
                },
              }}
              transformOrigin={{ horizontal: 'right', vertical: 'top' }}
              anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
            >
              <Box sx={{ px: 2, py: 1 }}>
                <Typography variant="subtitle1" fontWeight={600}>
                  {user.name || user.email}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {user.role}
                </Typography>
              </Box>
              <Divider />
              <MenuItem onClick={handleProfileClick}>
                <ListItemIcon>
                  <PersonIcon fontSize="small" />
                </ListItemIcon>
                {t('navigation.profile')}
              </MenuItem>
              <MenuItem onClick={handleSettingsClick}>
                <ListItemIcon>
                  <SettingsIcon fontSize="small" />
                </ListItemIcon>
                {t('common.settings')}
              </MenuItem>
              <Divider />
              <MenuItem onClick={handleLogout}>
                <ListItemIcon>
                  <LogoutIcon fontSize="small" />
                </ListItemIcon>
                {t('auth.logout')}
              </MenuItem>
            </Menu>
          </>
        ) : (
          <>
            <Button 
              color="inherit" 
              onClick={() => navigate('/login')}
              sx={{ ml: 1 }}
            >
              {t('auth.login')}
            </Button>
            <Button 
              variant="contained" 
              color="primary"
              onClick={() => navigate('/register')}
              sx={{ ml: 1, display: { xs: 'none', sm: 'block' } }}
            >
              {t('auth.register')}
            </Button>
          </>
        )}
      </Toolbar>
    </AppBar>
  );
};

export default Header;
