const mongoose = require('mongoose');

const TicketSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please add a ticket name'],
    trim: true,
    maxlength: [100, 'Name cannot be more than 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Please add a description'],
    maxlength: [500, 'Description cannot be more than 500 characters']
  },
  price: {
    type: Number,
    required: [true, 'Please add a price'],
    min: [0, 'Price must be a positive number']
  },
  type: {
    type: String,
    required: [true, 'Please specify ticket type'],
    enum: ['adult', 'child', 'senior', 'student', 'family', 'group', 'special']
  },
  validity: {
    type: Date,
    required: [true, 'Please add validity date']
  },
  maxVisitors: {
    type: Number,
    required: [true, 'Please specify maximum number of visitors'],
    min: [1, 'Maximum visitors must be at least 1']
  },
  availableFrom: {
    type: Date,
    required: [true, 'Please add available from date']
  },
  availableTo: {
    type: Date,
    required: [true, 'Please add available to date']
  },
  isActive: {
    type: Boolean,
    default: true
  },
  includes: [{
    type: String,
    trim: true
  }],
  exclusions: [{
    type: String,
    trim: true
  }],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
TicketSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('Ticket', TicketSchema);
