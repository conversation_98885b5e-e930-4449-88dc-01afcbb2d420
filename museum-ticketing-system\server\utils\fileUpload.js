const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const sharp = require('sharp');

// Configure storage
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../../public/uploads');
    
    // Create uploads directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
    const ext = path.extname(file.originalname).toLowerCase();
    cb(null, `${file.fieldname}-${uniqueSuffix}${ext}`);
  }
});

// File filter to allow only images
const imageFilter = (req, file, cb) => {
  const filetypes = /jpeg|jpg|png|gif|webp/;
  const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
  const mimetype = filetypes.test(file.mimetype);

  if (mimetype && extname) {
    return cb(null, true);
  } else {
    cb(new Error('Only image files are allowed!'), false);
  }
};

// Initialize multer with configuration
const upload = multer({
  storage: storage,
  fileFilter: imageFilter,
  limits: { fileSize: 5 * 1024 * 1024 } // 5MB limit
});

// Middleware for single file upload
const uploadSingle = (fieldName) => {
  return (req, res, next) => {
    upload.single(fieldName)(req, res, async (err) => {
      if (err) {
        return res.status(400).json({
          success: false,
          message: err.message
        });
      }
      
      // Process image if file was uploaded
      if (req.file) {
        try {
          // Generate thumbnail
          const thumbnailPath = path.join(
            path.dirname(req.file.path),
            `thumb-${req.file.filename}`
          );
          
          // Resize and optimize image
          await sharp(req.file.path)
            .resize(300, 200, {
              fit: 'cover',
              position: 'center'
            })
            .jpeg({ quality: 80 })
            .toFile(thumbnailPath);
          
          // Add file information to request object
          req.file.thumbnail = `/uploads/thumb-${req.file.filename}`;
          req.file.path = `/uploads/${req.file.filename}`;
          
          next();
        } catch (error) {
          console.error('Error processing image:', error);
          return res.status(500).json({
            success: false,
            message: 'Error processing image'
          });
        }
      } else {
        next();
      }
    });
  };
};

// Middleware for multiple file uploads
const uploadMultiple = (fieldName, maxCount = 5) => {
  return (req, res, next) => {
    upload.array(fieldName, maxCount)(req, res, async (err) => {
      if (err) {
        return res.status(400).json({
          success: false,
          message: err.message
        });
      }
      
      // Process each uploaded file
      if (req.files && req.files.length > 0) {
        try {
          const processedFiles = [];
          
          for (const file of req.files) {
            // Generate thumbnail
            const thumbnailPath = path.join(
              path.dirname(file.path),
              `thumb-${file.filename}`
            );
            
            // Resize and optimize image
            await sharp(file.path)
              .resize(300, 200, {
                fit: 'cover',
                position: 'center'
              })
              .jpeg({ quality: 80 })
              .toFile(thumbnailPath);
            
            processedFiles.push({
              ...file,
              thumbnail: `/uploads/thumb-${file.filename}`,
              path: `/uploads/${file.filename}`
            });
          }
          
          req.files = processedFiles;
          next();
        } catch (error) {
          console.error('Error processing images:', error);
          return res.status(500).json({
            success: false,
            message: 'Error processing images'
          });
        }
      } else {
        next();
      }
    });
  };
};

// Delete file from the server
const deleteFile = (filePath) => {
  if (!filePath) return;
  
  const fullPath = path.join(__dirname, '../../public', filePath);
  
  if (fs.existsSync(fullPath)) {
    fs.unlink(fullPath, (err) => {
      if (err) console.error(`Error deleting file: ${err}`);
    });
  }
};

module.exports = {
  upload,
  uploadSingle,
  uploadMultiple,
  deleteFile
};
