{"name": "museum-ticketing-server", "version": "1.0.0", "description": "Backend for Museum Ticketing System with Chatbot", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^8.1.0", "express-validator": "^7.0.1", "helmet": "^6.0.1", "hpp": "^0.2.3", "jsonwebtoken": "^9.0.0", "mongoose": "^7.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^7.0.6", "puppeteer": "^24.22.0", "qrcode": "^1.5.4", "rate-limiter-flexible": "^7.3.1", "socket.io": "^4.6.1", "stripe": "^12.1.1", "uuid": "^13.0.0", "winston": "^3.17.0", "xss-clean": "^0.1.4"}, "devDependencies": {"nodemon": "^2.0.22"}}