{"name": "museum-ticketing-server", "version": "1.0.0", "description": "Backend for Museum Ticketing System with Chatbot", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"express": "^4.18.2", "mongoose": "^7.0.3", "cors": "^2.8.5", "dotenv": "^16.0.3", "jsonwebtoken": "^9.0.0", "bcryptjs": "^2.4.3", "express-validator": "^7.0.1", "socket.io": "^4.6.1", "stripe": "^12.1.1", "multer": "^1.4.5-lts.1", "morgan": "^1.10.0", "helmet": "^6.0.1", "compression": "^1.7.4"}, "devDependencies": {"nodemon": "^2.0.22"}}