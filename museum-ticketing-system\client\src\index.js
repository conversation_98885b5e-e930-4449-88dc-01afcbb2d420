import React from 'react';
import ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import { CookiesProvider } from 'react-cookie';
import { I18nextProvider } from 'react-i18next';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { PersistGate } from 'redux-persist/integration/react';
import { persistStore } from 'redux-persist';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { enUS as dateFnsEn, faIR as dateFnsFa } from 'date-fns/locale';

import './index.css';
import 'react-toastify/dist/ReactToastify.css';
import 'leaflet/dist/leaflet.css';
import 'react-quill/dist/quill.snow.css';

import App from './App';
import store from './store/store';
import i18n from './i18n/i18n';
import reportWebVitals from './reportWebVitals';
import { selectLanguage } from './store/slices/uiSlice';

const dateFnsLocales = {
  en: dateFnsEn,
  fa: dateFnsFa,
};

const root = ReactDOM.createRoot(document.getElementById('root'));
const persistor = persistStore(store);

// Get initial language from store
let language = 'en';
store.subscribe(() => {
  const newLanguage = selectLanguage(store.getState());
  if (newLanguage !== language) {
    language = newLanguage;
  }
});

root.render(
  <React.StrictMode>
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <CookiesProvider>
          <I18nextProvider i18n={i18n}>
            <LocalizationProvider 
              dateAdapter={AdapterDateFns} 
              adapterLocale={dateFnsLocales[language]}
            >
              <BrowserRouter>
                <App />
              </BrowserRouter>
            </LocalizationProvider>
          </I18nextProvider>
        </CookiesProvider>
      </PersistGate>
    </Provider>
  </React.StrictMode>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
