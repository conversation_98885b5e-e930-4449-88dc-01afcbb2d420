import axios from 'axios';
import { getCookie } from 'react-cookie';
import { toast } from 'react-toastify';

// Create axios instance
const apiClient = axios.create({
  baseURL: process.env.REACT_APP_API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
});

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    const token = getCookie('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    const expectedError =
      error.response &&
      error.response.status >= 400 &&
      error.response.status < 500;

    if (!expectedError) {
      console.error('An unexpected error occurred:', error);
      toast.error('An unexpected error occurred');
    }

    // Handle specific error statuses
    if (error.response) {
      const { status, data } = error.response;
      
      if (status === 401) {
        // Handle unauthorized access
        toast.error('Session expired. Please log in again.');
        // Redirect to login or refresh token
      } else if (status === 403) {
        // Handle forbidden access
        toast.error('You do not have permission to perform this action.');
      } else if (status === 404) {
        // Handle not found
        toast.error('The requested resource was not found.');
      } else if (status === 500) {
        // Handle server error
        toast.error('A server error occurred. Please try again later.');
      } else if (data && data.message) {
        // Show error message from server
        toast.error(data.message);
      }
    }

    return Promise.reject(error);
  }
);

export default apiClient;
