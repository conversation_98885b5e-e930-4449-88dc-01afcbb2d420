const express = require('express');
const {
  getExhibitions,
  getExhibition,
  createExhibition,
  updateExhibition,
  deleteExhibition,
  getFeaturedExhibitions,
  getExhibitionsByStatus
} = require('../controllers/exhibitions');

const router = express.Router({ mergeParams: true });

const { protect, authorize } = require('../middleware/auth');

// Public routes
router.get('/', getExhibitions);
router.get('/featured', getFeaturedExhibitions);
router.get('/status/:status', getExhibitionsByStatus);
router.get('/:id', getExhibition);

// Protected routes (admin only)
router.use(protect, authorize('admin'));
router.post('/', createExhibition);
router.put('/:id', updateExhibition);
router.delete('/:id', deleteExhibition);

module.exports = router;
